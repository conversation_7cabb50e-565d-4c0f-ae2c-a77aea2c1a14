# Obsidian Titan Boss Pack for Minecraft 1.21+

A complete custom boss pack featuring the **Obsidian Titan** - a formidable armored titan with glowing crystal spikes.

## Features

### Boss Characteristics
- **Name**: Obsidian Titan
- **Health**: 500 HP
- **Damage**: 12 per hit
- **Appearance**: Humanoid armored titan with glowing cyan crystal spikes
- **Boss Bar**: Displays health with red progress bar

### Model Details
- **Articulated Parts**: Head with horns, bulky torso with crystal core, spiked arms, beast-like legs
- **Special Features**: Glowing back crystal cluster
- **Texture**: 128x128 with dark obsidian black, crimson red, and cyan highlights
- **Animations**: Idle (crystal pulsing), attack (arm slam), hurt (flinch), death (crystal shatter)

### Custom Drops
- **Crystal Heart**: Rare epic item with enchanted glow
- **Obsidian**: 5-10 pieces
- **Diamonds**: 2-5 pieces  
- **Emeralds**: 1-3 pieces
- **Experience Bottles**: 50-100 bottles

## Installation

1. **Resource Pack**: Copy the `resourcepack` folder to your Minecraft `resourcepacks` directory
2. **Datapack**: Copy the `datapack` folder to your world's `datapacks` directory
3. **Behavior Pack** (Bedrock): Copy the `behaviorpack` folder to your `behavior_packs` directory

## Usage

### Basic Summoning
```mcfunction
/function obsidian_titan:summon
```

### Cinematic Summoning (Recommended)
```mcfunction
/function obsidian_titan:summon_cinematic
```

### Direct Entity Summon
```mcfunction
/summon zombie ~ ~ ~ {CustomName:'"§4Obsidian Titan"',Health:500f,Tags:["obsidian_titan"]}
```

## File Structure

```
├── resourcepack/
│   ├── pack.mcmeta
│   ├── models/entity/obsidian_titan.json
│   ├── textures/entity/
│   │   ├── obsidian_titan.png
│   │   └── obsidian_titan_glow.png
│   ├── textures/items/crystal_heart.png
│   ├── animations/obsidian_titan.json
│   ├── animation_controllers/obsidian_titan.json
│   ├── entity/obsidian_titan.json
│   └── render_controllers/obsidian_titan.json
├── datapack/
│   ├── pack.mcmeta
│   └── data/obsidian_titan/
│       ├── functions/
│       ├── predicates/
│       └── advancements/
├── behaviorpack/
│   ├── pack.mcmeta
│   ├── entities/obsidian_titan.json
│   ├── items/crystal_heart.json
│   └── loot_tables/entities/obsidian_titan.json
└── generate_texture.py
```

## Technical Details

- **Pack Format**: 26 (Minecraft 1.21+)
- **Model Format**: Bedrock-style JSON geometry
- **Texture Resolution**: 128x128 for entity, 16x16 for items
- **Animation System**: Molang-based with state controllers

## Effects

### Visual Effects
- Crystal pulsing during idle
- Particle trails (cyan dust, obsidian tears)
- Glowing overlay textures
- Death explosion with crystal shatter

### Audio Effects
- Custom ambient sounds (deep rumble)
- Attack sound effects
- Death sound sequence

### Cinematic Features
- Lightning strikes on spawn
- Obsidian platform creation
- Screen shake effects
- Dramatic particle displays

## Compatibility

- **Minecraft Java Edition**: 1.21+
- **Minecraft Bedrock Edition**: 1.21+
- **Pack Format**: 26

## Credits

Created for Minecraft 1.21+ with drag-and-drop compatibility.
Includes both Java Edition datapack and Bedrock Edition behavior pack formats.
