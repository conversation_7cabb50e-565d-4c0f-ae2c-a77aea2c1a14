# Tick function to manage boss behavior and effects

# Update boss bar health
execute as @e[type=zombie,tag=obsidian_titan,tag=boss_active] store result bossbar obsidian_titan value run data get entity @s Health

# Remove boss bar when boss dies
execute unless entity @e[type=zombie,tag=obsidian_titan,tag=boss_active] run bossbar remove obsidian_titan

# Particle effects while boss is alive
execute at @e[type=zombie,tag=obsidian_titan,tag=boss_active] run particle dust 0 1 1 1 ~ ~1 ~ 0.5 0.5 0.5 0.1 2 force @a[distance=..50]
execute at @e[type=zombie,tag=obsidian_titan,tag=boss_active] run particle falling_obsidian_tear ~ ~0.1 ~ 1 0 1 0.1 1 force @a[distance=..50]
execute at @e[type=zombie,tag=obsidian_titan,tag=boss_active] run particle end_rod ~ ~2 ~ 0.3 0.3 0.3 0.02 1 force @a[distance=..50]

# Walking particle effects
execute at @e[type=zombie,tag=obsidian_titan,tag=boss_active,nbt={OnGround:1b}] if predicate obsidian_titan:random_chance run particle block obsidian ~ ~0.1 ~ 0.5 0.1 0.5 0.1 3 force @a[distance=..50]

# Ambient sound effects
execute at @e[type=zombie,tag=obsidian_titan,tag=boss_active] if predicate obsidian_titan:random_chance run playsound block.beacon.ambient master @a[distance=..30] ~ ~ ~ 0.3 0.5

# Boss death effects
execute at @e[type=zombie,tag=obsidian_titan,nbt={Health:0.0f}] run function obsidian_titan:death_effects
