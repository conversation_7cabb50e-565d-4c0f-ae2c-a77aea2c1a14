#!/usr/bin/env python3
"""
Obsidian Titan Texture Generator
Creates a 128x128 texture for the Obsidian Titan boss model
"""

from PIL import Image, ImageDraw
import numpy as np

def create_obsidian_titan_texture():
    # Create 128x128 texture
    width, height = 128, 128
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Color palette
    obsidian_black = (20, 20, 25, 255)
    dark_obsidian = (10, 10, 15, 255)
    crimson_red = (139, 0, 0, 255)
    bright_crimson = (220, 20, 60, 255)
    cyan_glow = (0, 255, 255, 255)
    cyan_dim = (0, 200, 200, 255)
    gray_metal = (64, 64, 64, 255)
    
    # Fill background with obsidian black
    draw.rectangle([0, 0, width, height], fill=obsidian_black)
    
    # Head texture (0, 0, 32, 32)
    # Main head
    draw.rectangle([0, 0, 32, 32], fill=obsidian_black)
    # Face details
    draw.rectangle([8, 8, 24, 24], fill=dark_obsidian)
    # Eyes (glowing cyan)
    draw.rectangle([10, 12, 14, 16], fill=cyan_glow)
    draw.rectangle([18, 12, 22, 16], fill=cyan_glow)
    # Mouth/jaw
    draw.rectangle([12, 20, 20, 24], fill=crimson_red)
    
    # Horns (32, 0, 48, 16)
    draw.rectangle([32, 0, 40, 16], fill=gray_metal)  # Left horn
    draw.rectangle([40, 0, 48, 16], fill=gray_metal)  # Right horn
    # Horn tips (cyan glow)
    draw.rectangle([34, 2, 38, 6], fill=cyan_dim)
    draw.rectangle([42, 2, 46, 6], fill=cyan_dim)
    
    # Body texture (16, 16, 48, 32)
    draw.rectangle([16, 16, 48, 32], fill=obsidian_black)
    # Armor plating
    draw.rectangle([18, 18, 30, 30], fill=dark_obsidian)
    draw.rectangle([34, 18, 46, 30], fill=dark_obsidian)
    # Crimson details
    draw.rectangle([20, 20, 28, 22], fill=crimson_red)
    draw.rectangle([36, 20, 44, 22], fill=crimson_red)
    
    # Crystal core (0, 32, 16, 48)
    draw.rectangle([0, 32, 16, 48], fill=cyan_dim)
    # Core center (bright glow)
    draw.rectangle([4, 36, 12, 44], fill=cyan_glow)
    # Core details
    draw.rectangle([6, 38, 10, 42], fill=(255, 255, 255, 255))
    
    # Arms (40, 16, 56, 48)
    # Left arm
    draw.rectangle([40, 16, 48, 32], fill=obsidian_black)
    draw.rectangle([42, 18, 46, 30], fill=dark_obsidian)
    # Right arm
    draw.rectangle([40, 32, 48, 48], fill=obsidian_black)
    draw.rectangle([42, 34, 46, 46], fill=dark_obsidian)
    
    # Arm spikes (56, 16, 72, 48)
    # Left arm spikes
    draw.rectangle([56, 16, 64, 32], fill=gray_metal)
    draw.rectangle([58, 18, 62, 22], fill=cyan_dim)
    draw.rectangle([58, 26, 62, 30], fill=cyan_dim)
    # Right arm spikes
    draw.rectangle([56, 32, 64, 48], fill=gray_metal)
    draw.rectangle([58, 34, 62, 38], fill=cyan_dim)
    draw.rectangle([58, 42, 62, 46], fill=cyan_dim)
    
    # Legs (0, 16, 16, 32 and 16, 32, 32, 48)
    # Left leg
    draw.rectangle([0, 16, 16, 32], fill=obsidian_black)
    draw.rectangle([2, 18, 14, 30], fill=dark_obsidian)
    draw.rectangle([4, 20, 12, 22], fill=crimson_red)
    # Right leg
    draw.rectangle([16, 32, 32, 48], fill=obsidian_black)
    draw.rectangle([18, 34, 30, 46], fill=dark_obsidian)
    draw.rectangle([20, 36, 28, 38], fill=crimson_red)
    
    # Back crystal cluster (64, 0, 96, 32)
    draw.rectangle([64, 0, 96, 32], fill=cyan_dim)
    # Main crystal
    draw.rectangle([68, 4, 92, 28], fill=cyan_glow)
    # Crystal facets
    draw.rectangle([72, 8, 88, 24], fill=(255, 255, 255, 255))
    # Smaller crystals
    draw.rectangle([78, 0, 86, 8], fill=cyan_glow)
    draw.rectangle([66, 12, 74, 20], fill=cyan_glow)
    draw.rectangle([86, 16, 94, 24], fill=cyan_glow)
    
    # Add some texture details and noise for realism
    for i in range(0, width, 4):
        for j in range(0, height, 4):
            if np.random.random() > 0.8:
                # Add small dark spots for texture
                draw.rectangle([i, j, i+1, j+1], fill=dark_obsidian)
            elif np.random.random() > 0.95:
                # Add small bright spots for highlights
                draw.rectangle([i, j, i+1, j+1], fill=gray_metal)
    
    return img

def create_glow_overlay():
    """Create a separate glow overlay texture"""
    width, height = 128, 128
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    cyan_glow = (0, 255, 255, 128)  # Semi-transparent
    
    # Eyes glow
    draw.ellipse([9, 11, 15, 17], fill=cyan_glow)
    draw.ellipse([17, 11, 23, 17], fill=cyan_glow)
    
    # Crystal core glow
    draw.ellipse([2, 34, 14, 46], fill=cyan_glow)
    
    # Back crystal glow
    draw.ellipse([66, 2, 94, 30], fill=cyan_glow)
    
    # Horn tips glow
    draw.ellipse([33, 1, 39, 7], fill=cyan_glow)
    draw.ellipse([41, 1, 47, 7], fill=cyan_glow)
    
    return img

def create_crystal_heart_texture():
    """Create a 16x16 crystal heart item texture"""
    width, height = 16, 16
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # Colors
    cyan_bright = (0, 255, 255, 255)
    cyan_medium = (0, 200, 200, 255)
    cyan_dark = (0, 150, 150, 255)
    white = (255, 255, 255, 255)

    # Heart shape outline
    heart_pixels = [
        (7, 3), (8, 3),
        (6, 4), (7, 4), (8, 4), (9, 4),
        (5, 5), (6, 5), (7, 5), (8, 5), (9, 5), (10, 5),
        (4, 6), (5, 6), (6, 6), (7, 6), (8, 6), (9, 6), (10, 6), (11, 6),
        (4, 7), (5, 7), (6, 7), (7, 7), (8, 7), (9, 7), (10, 7), (11, 7),
        (5, 8), (6, 8), (7, 8), (8, 8), (9, 8), (10, 8),
        (6, 9), (7, 9), (8, 9), (9, 9),
        (7, 10), (8, 10),
        (7, 11)
    ]

    # Fill heart with cyan
    for x, y in heart_pixels:
        draw.point((x, y), fill=cyan_medium)

    # Add highlights
    highlight_pixels = [(7, 4), (8, 5), (6, 6), (9, 7)]
    for x, y in highlight_pixels:
        draw.point((x, y), fill=white)

    # Add crystal facets
    facet_pixels = [(7, 6), (8, 7), (7, 8)]
    for x, y in facet_pixels:
        draw.point((x, y), fill=cyan_bright)

    return img

if __name__ == "__main__":
    # Generate main texture
    texture = create_obsidian_titan_texture()
    texture.save("resourcepack/textures/entity/obsidian_titan.png")
    print("Generated obsidian_titan.png")

    # Generate glow overlay
    glow = create_glow_overlay()
    glow.save("resourcepack/textures/entity/obsidian_titan_glow.png")
    print("Generated obsidian_titan_glow.png")

    # Generate crystal heart item texture
    crystal_heart = create_crystal_heart_texture()
    crystal_heart.save("resourcepack/textures/items/crystal_heart.png")
    print("Generated crystal_heart.png")

    print("Texture generation complete!")
