@echo off
echo Installing Obsidian Titan Boss Pack...
echo.

echo Checking for Minecraft directories...

set "MINECRAFT_DIR=%APPDATA%\.minecraft"
set "RESOURCEPACK_DIR=%MINECRAFT_DIR%\resourcepacks"
set "SAVES_DIR=%MINECRAFT_DIR%\saves"

if not exist "%MINECRAFT_DIR%" (
    echo Minecraft directory not found at %MINECRAFT_DIR%
    echo Please install Minecraft Java Edition first.
    pause
    exit /b 1
)

echo Found Minecraft directory: %MINECRAFT_DIR%

if not exist "%RESOURCEPACK_DIR%" (
    echo Creating resourcepacks directory...
    mkdir "%RESOURCEPACK_DIR%"
)

echo Copying resource pack...
xcopy /E /I /Y "resourcepack" "%RESOURCEPACK_DIR%\Obsidian_Titan_Pack"

echo.
echo Resource pack installed successfully!
echo.
echo To install the datapack:
echo 1. Open your Minecraft world
echo 2. Copy the 'datapack' folder to your world's 'datapacks' directory
echo 3. Run /reload in-game
echo.
echo To summon the Obsidian Titan:
echo /function obsidian_titan:summon_cinematic
echo.
pause
