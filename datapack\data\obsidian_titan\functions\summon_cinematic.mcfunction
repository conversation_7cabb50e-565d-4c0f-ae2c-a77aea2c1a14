# Cinematic summoning of the Obsidian Titan
# Creates dramatic effects before spawning the boss

# Clear weather and set to night for dramatic effect
weather clear
time set midnight

# Create lightning strikes around the summoning area
summon lightning_bolt ~3 ~ ~3
summon lightning_bolt ~-3 ~ ~-3
summon lightning_bolt ~3 ~ ~-3
summon lightning_bolt ~-3 ~ ~3

# Wait a moment then summon the boss
schedule function obsidian_titan:summon_delayed 2s

# Create particle effects
particle explosion ~ ~1 ~ 2 2 2 0.1 10 force @a[distance=..50]
particle large_smoke ~ ~1 ~ 3 3 3 0.1 20 force @a[distance=..50]

# Screen shake effect (title with motion)
title @a[distance=..50] times 0 40 20
title @a[distance=..50] title {"text":"","color":"red"}
title @a[distance=..50] subtitle {"text":"§4§lSomething ancient stirs...","bold":true}

# Play dramatic sound
playsound entity.ender_dragon.growl master @a[distance=..50] ~ ~ ~ 2.0 0.3
playsound block.portal.trigger master @a[distance=..50] ~ ~ ~ 1.0 0.5

# Create obsidian platform
fill ~-2 ~-1 ~-2 ~2 ~-1 ~2 obsidian replace air
fill ~-1 ~-1 ~-1 ~1 ~-1 ~1 crying_obsidian replace obsidian
