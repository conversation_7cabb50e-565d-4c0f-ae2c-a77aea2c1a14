# Summon the Obsidian Titan boss
summon zombie ~ ~ ~ {CustomName:'"§4Obsidian Titan"',Health:500f,Attributes:[{Name:generic.max_health,Base:500},{Name:generic.attack_damage,Base:12},{Name:generic.movement_speed,Base:0.25}],Silent:1b,PersistenceRequired:1b,CanPickUpLoot:0b,Tags:["obsidian_titan","boss"],ArmorItems:[{},{},{},{id:"minecraft:player_head",Count:1b,tag:{SkullOwner:{Id:[I;-**********,987654321,-**********,667788990],Properties:{textures:[{Value:"eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvYjc5MjcyNjU4ZTVhZjg4NzE0Y2I5YzM2NmIzOGJhZjgxNGY3MTY2YjE3ZjMwNzJkMzc3YWI0ZGE4YzUwIn19fQ=="}]}}}}],ArmorDropChances:[0.0f,0.0f,0.0f,0.0f]}

# Add boss bar
bossbar add obsidian_titan "§4Obsidian Titan"
bossbar set obsidian_titan color red
bossbar set obsidian_titan style progress
bossbar set obsidian_titan max 500
bossbar set obsidian_titan value 500
bossbar set obsidian_titan players @a[distance=..50]

# Tag the summoned entity for tracking
tag @e[type=zombie,tag=obsidian_titan,distance=..5] add boss_active

# Display message
tellraw @a[distance=..50] {"text":"§4§lThe Obsidian Titan has awakened!","bold":true}

# Play sound effect
playsound entity.wither.spawn master @a[distance=..50] ~ ~ ~ 1.0 0.5
