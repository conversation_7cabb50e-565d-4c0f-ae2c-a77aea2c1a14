{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "obsidian_titan:obsidian_titan", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"obsidian_titan_adult": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 500 : 0"}, "minecraft:loot": {"table": "loot_tables/entities/obsidian_titan.json"}}}, "components": {"minecraft:type_family": {"family": ["obsidian_titan", "boss", "monster"]}, "minecraft:collision_box": {"width": 1.8, "height": 3.6}, "minecraft:health": {"value": 500, "max": 500}, "minecraft:attack": {"damage": 12}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.melee_attack": {"priority": 1, "speed_multiplier": 1.25, "attack_duration": 1.5}, "minecraft:behavior.random_stroll": {"priority": 2, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 3, "look_distance": 16.0}, "minecraft:behavior.random_look_around": {"priority": 4}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:boss": {"should_darken_sky": false, "hud_range": 55}, "minecraft:nameable": {}, "minecraft:ambient_sound_interval": {"value": 2.0, "range": 4.0, "event_name": "ambient"}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["obsidian_titan_adult"]}}}}}