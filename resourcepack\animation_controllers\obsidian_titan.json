{"format_version": "1.10.0", "animation_controllers": {"controller.animation.obsidian_titan.base": {"initial_state": "idle", "states": {"idle": {"animations": ["idle"], "transitions": [{"attack": "query.is_attacking"}, {"hurt": "query.health < query.max_health && query.hurt_time > 0"}, {"death": "query.health <= 0"}]}, "attack": {"animations": ["attack"], "transitions": [{"idle": "!query.is_attacking"}]}, "hurt": {"animations": ["hurt"], "transitions": [{"idle": "query.hurt_time <= 0"}, {"death": "query.health <= 0"}]}, "death": {"animations": ["death"]}}}}}